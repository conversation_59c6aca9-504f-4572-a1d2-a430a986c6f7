import { querySpace } from "@/app/api/read/space/query";
import { auth } from "@/auth";
import { redirect } from "next/navigation";

export async function generateMetadata() {
  const data = await querySpace();

  if (!data) {
    return {
      title: "Dashboard | Content Manager",
      description: "Content management dashboard",
    };
  }

  const { title, description } = data;

  return {
    title: "Dashboard" + " | " + title,
    description: description,
  };
}

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const res = await auth();

  // If not logged in, redirect to login page
  if (!res?.user) {
    redirect("/login");
  }

  // If logged in but not admin, show unauthorized message
  if (res?.user.role !== "ADMIN") {
    return (
      <div className="w-full flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full text-center pb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Unauthorized
          </h2>
          <p className="text-gray-600 mb-6">
            You don&apos;t have permission to access this dashboard. Please
            contact an administrator for access.
          </p>
          <p className="text-sm text-gray-500">
            Logged in as: {res.user.email}
          </p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
