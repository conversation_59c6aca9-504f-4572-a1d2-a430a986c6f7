import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Building2, Settings, FileText } from "lucide-react";

export const DashboardHomePage = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight text-foreground">
          Dashboard
        </h1>
        <p className="text-muted-foreground">
          Welcome to your content management dashboard
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              Space Management
            </CardTitle>
            <CardDescription>
              Manage your spaces, configurations, and content organization
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild className="w-full">
              <Link href="/dashboard/spaces">
                <Settings className="h-4 w-4 mr-2" />
                Manage Spaces
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Quick Actions
            </CardTitle>
            <CardDescription>
              Common tasks and quick access to frequently used features
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Button
                asChild
                variant="outline"
                className="w-full justify-start"
              >
                <Link href="/dashboard/spaces">View All Spaces</Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Getting Started</CardTitle>
            <CardDescription>
              New to the platform? Start here to set up your first space
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild variant="outline" className="w-full">
              <Link href="/dashboard/spaces">Create Your First Space</Link>
            </Button>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>About Space-Based Management</CardTitle>
          <CardDescription>
            Understanding how content is organized in this system
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 text-sm text-muted-foreground">
            <p>
              This platform uses a space-based approach to content management.
              Each space is an isolated environment with its own:
            </p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>Articles and blog posts</li>
              <li>Configuration settings</li>
              <li>Categories and tags</li>
              <li>Environment variables</li>
            </ul>
            <p>
              To get started, create a space and begin adding your content. Each
              space can be configured independently, allowing you to manage
              multiple projects or environments from a single dashboard.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
