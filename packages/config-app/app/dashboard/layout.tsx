"use client";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { useRef } from "react";

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const queryClient = useRef(new QueryClient());

  return (
    <QueryClientProvider client={queryClient.current}>
      <div className="w-full min-h-screen bg-background">
        <main className="w-full px-12 py-4 flex-auto flex flex-col">
          {children}
        </main>
      </div>
    </QueryClientProvider>
  );
}
