"use client";
import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { Save, Eye, EyeOff, ArrowLeft } from "lucide-react";
import { useState } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";

const configSchema = z.object({
  authGoogleId: z.string().optional(),
  authGoogleSecret: z.string().optional(),
  s3BucketName: z.string().optional(),
  awsRegion: z.string().optional(),
  awsAccessKeyId: z.string().optional(),
  awsSecretAccessKey: z.string().optional(),
  gaMeasurementId: z.string().optional(),
  openaiApiKey: z.string().optional(),
  googleApiKey: z.string().optional(),
  nextPublicStripePublishableKey: z.string().optional(),
  stripeSecretKey: z.string().optional(),
  stripeWebhookSecret: z.string().optional(),
  nextPublicFaviconUrl: z.string().optional(),
});

type ConfigFormValues = z.infer<typeof configSchema>;

const configFields = [
  {
    section: "Authentication",
    fields: [
      {
        name: "authGoogleId",
        label: "Google OAuth Client ID",
        type: "text",
        description: "Google OAuth 2.0 client ID for authentication",
      },
      {
        name: "authGoogleSecret",
        label: "Google OAuth Client Secret",
        type: "password",
        description: "Google OAuth 2.0 client secret",
      },
    ],
  },
  {
    section: "AWS Configuration",
    fields: [
      {
        name: "s3BucketName",
        label: "S3 Bucket Name",
        type: "text",
        description: "AWS S3 bucket name for file storage",
      },
      {
        name: "awsRegion",
        label: "AWS Region",
        type: "text",
        description: "AWS region (e.g., us-east-1)",
      },
      {
        name: "awsAccessKeyId",
        label: "AWS Access Key ID",
        type: "text",
        description: "AWS access key ID",
      },
      {
        name: "awsSecretAccessKey",
        label: "AWS Secret Access Key",
        type: "password",
        description: "AWS secret access key",
      },
    ],
  },
  {
    section: "Analytics & APIs",
    fields: [
      {
        name: "gaMeasurementId",
        label: "Google Analytics Measurement ID",
        type: "text",
        description: "Google Analytics 4 measurement ID (e.g., G-XXXXXXXXXX)",
      },
      {
        name: "openaiApiKey",
        label: "OpenAI API Key",
        type: "password",
        description: "OpenAI API key for AI features",
      },
      {
        name: "googleApiKey",
        label: "Google API Key",
        type: "password",
        description: "Google API key for various Google services",
      },
    ],
  },
  {
    section: "Stripe Configuration",
    fields: [
      {
        name: "nextPublicStripePublishableKey",
        label: "Stripe Publishable Key",
        type: "text",
        description: "Stripe publishable key (safe to expose in frontend)",
      },
      {
        name: "stripeSecretKey",
        label: "Stripe Secret Key",
        type: "password",
        description: "Stripe secret key for server-side operations",
      },
      {
        name: "stripeWebhookSecret",
        label: "Stripe Webhook Secret",
        type: "password",
        description: "Stripe webhook endpoint secret",
      },
    ],
  },
  {
    section: "UI Configuration",
    fields: [
      {
        name: "nextPublicFaviconUrl",
        label: "Favicon URL",
        type: "text",
        description: "URL to the favicon image",
      },
    ],
  },
] as const;

interface SpaceConfig extends ConfigFormValues {
  id: string;
  title: string;
  description: string;
}

export function SpaceConfigurationPage() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const params = useParams();
  const spaceId = params.spaceId as string;
  const [showPasswords, setShowPasswords] = useState<Record<string, boolean>>(
    {},
  );

  const form = useForm<ConfigFormValues>({
    resolver: zodResolver(configSchema),
    defaultValues: {},
  });

  const { data: config, isLoading } = useQuery({
    queryKey: ["space-config", spaceId],
    queryFn: async () => {
      const response = await fetch(`/api/manage/spaces/${spaceId}/config`);
      if (!response.ok) {
        throw new Error("Failed to fetch configuration");
      }
      return response.json() as Promise<SpaceConfig>;
    },
    enabled: !!spaceId,
  });

  const updateConfigMutation = useMutation({
    mutationFn: async (data: ConfigFormValues) => {
      const response = await fetch(`/api/manage/spaces/${spaceId}/config`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to update configuration");
      }

      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Configuration updated",
        description: "Your configuration has been saved successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ["space-config", spaceId] });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  React.useEffect(() => {
    if (config) {
      form.reset(config);
    }
  }, [config, form]);

  const onSubmit = (data: ConfigFormValues) => {
    updateConfigMutation.mutate(data);
  };

  const togglePasswordVisibility = (fieldName: string) => {
    setShowPasswords((prev) => ({
      ...prev,
      [fieldName]: !prev[fieldName],
    }));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-lg">Loading configuration...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/dashboard/spaces">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Spaces
          </Link>
        </Button>
      </div>

      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-foreground">
            Configuration
          </h1>
          <p className="text-muted-foreground">
            {`Manage configuration settings for "${config?.title}"`}
          </p>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {configFields.map((section) => (
            <Card key={section.section}>
              <CardHeader>
                <CardTitle>{section.section}</CardTitle>
                <CardDescription>
                  Configure {section.section.toLowerCase()} settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {section.fields.map((field) => (
                  <FormField
                    key={field.name}
                    control={form.control}
                    name={field.name as keyof ConfigFormValues}
                    render={({ field: formField }) => (
                      <FormItem>
                        <FormLabel>{field.label}</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input
                              {...formField}
                              type={
                                field.type === "password" &&
                                !showPasswords[field.name]
                                  ? "password"
                                  : "text"
                              }
                              placeholder={`Enter ${field.label.toLowerCase()}`}
                              value={formField.value || ""}
                            />
                            {field.type === "password" && (
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                onClick={() =>
                                  togglePasswordVisibility(field.name)
                                }
                              >
                                {showPasswords[field.name] ? (
                                  <EyeOff className="h-4 w-4" />
                                ) : (
                                  <Eye className="h-4 w-4" />
                                )}
                              </Button>
                            )}
                          </div>
                        </FormControl>
                        <FormDescription>{field.description}</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                ))}
              </CardContent>
            </Card>
          ))}

          <div className="flex justify-end">
            <Button
              type="submit"
              disabled={updateConfigMutation.isPending}
              className="min-w-[120px]"
            >
              {updateConfigMutation.isPending ? (
                "Saving..."
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Configuration
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
