"use client";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";

const formSchema = z.object({
  title: z.string().min(1, "Title is required"),
  language: z.enum(["en", "jp", "zh"]),
});

type FormValues = z.infer<typeof formSchema>;

interface SpaceCreateArticleModalProps {
  spaceId: string;
  onSuccess?: () => void;
}

export function SpaceCreateArticleModal({
  spaceId,
  onSuccess,
}: SpaceCreateArticleModalProps) {
  const [open, setOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const { toast } = useToast();
  const router = useRouter();

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      language: "en",
      title: "",
    },
  });

  const createArticle = async (values: FormValues) => {
    const articleData = {
      blog: {
        articleDate: new Date().toISOString(),
        isPremium: false,
        isPublished: false,
        slug: "",
        tags: [],
        categories: [],
      },
      content: {
        title: values.title,
        imageUrl: "",
        seoDescription: "",
        content: "",
        language: values.language,
      },
    };

    const response = await fetch(`/api/manage/spaces/${spaceId}/articles`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(articleData),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to create article");
    }

    return response.json();
  };

  const onSubmit = async (values: FormValues) => {
    try {
      setIsCreating(true);
      const result = await createArticle(values);

      toast({
        title: "Article created",
        description: "Your article has been created successfully.",
      });

      setOpen(false);
      form.reset();
      onSuccess?.();

      // Navigate to edit page
      router.push(
        `/dashboard/spaces/${spaceId}/articles/${result.blog.id}/edit`,
      );
    } catch (error) {
      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to create article",
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  const handleClose = () => {
    setOpen(false);
    form.reset();
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          New Article
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create a new article</DialogTitle>
          <DialogDescription>
            Choose a language and enter a title for your new article.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="language"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Language</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="grid grid-cols-3 gap-4 pt-2"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="en" id="en" />
                          <Label htmlFor="en">English</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="jp" id="jp" />
                          <Label htmlFor="jp">Japanese</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="zh" id="zh" />
                          <Label htmlFor="zh">Chinese</Label>
                        </div>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Title</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter article title" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="flex justify-end gap-3">
              <Button type="button" variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={isCreating}>
                {isCreating ? "Creating..." : "Create Article"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
