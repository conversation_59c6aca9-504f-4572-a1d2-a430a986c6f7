"use client";
import React from "react";
import { useMutation, useQuery } from "@tanstack/react-query";
import Link from "next/link";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { ArrowLeft, Wand2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { useParams } from "next/navigation";
import slugify from "slugify";
import { ContentField } from "@/app/dashboard/components/content-field";
import UploadImage from "@/app/dashboard/components/upload-image";
import { SeoSuggestButton } from "@/app/dashboard/components/seo-suggestion-button";

const formSchema = z.object({
  title: z.string().min(1, "Title is required"),
  content: z.string().min(1, "Content is required"),
  seoDescription: z.string().optional(),
  imageUrl: z.string().optional(),
  slug: z.string().optional(),
  articleDate: z.string().optional(),
  language: z.enum(["en", "jp", "zh"]),
  isPublished: z.boolean(),
  isPremium: z.boolean(),
  isReady: z.boolean(),
  categories: z.array(z.string()),
  tags: z.array(z.string()),
});

export type FormValues = z.infer<typeof formSchema>;

interface BlogContent {
  id: string;
  title: string;
  language: string;
  imageUrl: string;
  seoDescription: string;
  content: string;
  isReady: boolean;
}

interface Blog {
  id: string;
  slug: string;
  articleDate: string;
  isPremium: boolean;
  isPublished: boolean;
  tags: string[];
  contents: BlogContent[];
  categories: Array<{
    id: string;
    labels: Array<{
      id: string;
      name: string;
      language: string;
    }>;
  }>;
}

interface Category {
  id: string;
  labels: Array<{
    id: string;
    name: string;
    language: string;
  }>;
}

export function SpaceEditArticlePage() {
  const { toast } = useToast();
  const params = useParams();
  const spaceId = params.spaceId as string;
  const articleId = params.articleId as string;

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: "",
      content: "",
      seoDescription: "",
      imageUrl: "",
      slug: "",
      articleDate: "",
      language: "en",
      isPublished: false,
      isPremium: false,
      isReady: false,
      categories: [],
      tags: [],
    },
  });

  const { isPending, data } = useQuery({
    queryFn: async () => {
      const res = await fetch(
        `/api/manage/spaces/${spaceId}/articles/${articleId}`,
      );
      if (!res.ok) {
        throw new Error("Failed to fetch article");
      }
      return res.json() as Promise<Blog>;
    },
    queryKey: ["space-article", spaceId, articleId],
    enabled: !!spaceId && !!articleId,
  });

  const { data: categories } = useQuery({
    queryFn: async () => {
      const res = await fetch("/api/read/category");
      return res.json() as Promise<Category[]>;
    },
    queryKey: ["categories"],
  });

  React.useEffect(() => {
    if (data) {
      const primaryContent =
        data.contents.find((c) => c.language === "en") || data.contents[0];
      if (primaryContent) {
        form.reset({
          title: primaryContent.title,
          content: primaryContent.content,
          seoDescription: primaryContent.seoDescription,
          imageUrl: primaryContent.imageUrl,
          slug: data.slug,
          articleDate: data.articleDate
            ? new Date(data.articleDate).toISOString().split("T")[0]
            : "",
          language: primaryContent.language as "en" | "jp" | "zh",
          isPublished: data.isPublished,
          isPremium: data.isPremium,
          isReady: primaryContent.isReady,
          categories: data.categories.map((c) => c.id),
          tags: data.tags || [],
        });
      }
    }
  }, [data, form]);

  const mutation = useMutation({
    mutationFn: async (values: FormValues) => {
      const updateData = {
        blog: {
          articleDate: values.articleDate
            ? new Date(values.articleDate).toISOString()
            : undefined,
          isPremium: values.isPremium,
          isPublished: values.isPublished,
          slug: values.slug || "",
          categories: values.categories,
          tags: values.tags,
        },
        content: {
          title: values.title,
          imageUrl: values.imageUrl || "",
          seoDescription: values.seoDescription || "",
          content: values.content,
          language: values.language,
          isReady: values.isReady,
        },
      };

      const response = await fetch(
        `/api/manage/spaces/${spaceId}/articles/${articleId}`,
        {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(updateData),
        },
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to update article");
      }

      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Article updated",
        description: "Your article has been saved successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const onSubmit = (values: FormValues) => {
    mutation.mutate(values);
  };

  const generateSlug = () => {
    const title = form.getValues("title");
    if (title) {
      const slug = slugify(title, { lower: true, strict: true });
      form.setValue("slug", slug);
    }
  };

  if (isPending) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-lg">Loading article...</div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-lg text-muted-foreground">Article not found</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href={`/dashboard/spaces/${spaceId}/articles`}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Articles
          </Link>
        </Button>
      </div>

      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-foreground">
            Edit Article
          </h1>
          <p className="text-muted-foreground">
            Update your article content and settings
          </p>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-6">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Title</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter article title" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="content"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Content</FormLabel>
                    <FormControl>
                      <ContentField
                        value={field.value}
                        onChange={field.onChange}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Settings</h3>

                <FormField
                  control={form.control}
                  name="language"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Language</FormLabel>
                      <FormControl>
                        <RadioGroup
                          onValueChange={field.onChange}
                          value={field.value}
                          className="flex gap-4"
                        >
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="en" id="edit-en" />
                            <Label htmlFor="edit-en">EN</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="jp" id="edit-jp" />
                            <Label htmlFor="edit-jp">JP</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="zh" id="edit-zh" />
                            <Label htmlFor="edit-zh">ZH</Label>
                          </div>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="articleDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Article Date</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="slug"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Slug</FormLabel>
                      <div className="flex gap-2">
                        <FormControl>
                          <Input placeholder="article-slug" {...field} />
                        </FormControl>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={generateSlug}
                        >
                          <Wand2 className="h-4 w-4" />
                        </Button>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="space-y-3">
                  <FormField
                    control={form.control}
                    name="isReady"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>Ready for review</FormLabel>
                        </div>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="isPublished"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>Published</FormLabel>
                        </div>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="isPremium"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>Premium content</FormLabel>
                        </div>
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-medium">SEO & Media</h3>

                <FormField
                  control={form.control}
                  name="imageUrl"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Featured Image</FormLabel>
                      <FormControl>
                        <UploadImage
                          value={field.value}
                          onChange={field.onChange}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="seoDescription"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>SEO Description</FormLabel>
                      <div className="space-y-2">
                        <FormControl>
                          <Textarea
                            placeholder="Enter SEO description"
                            {...field}
                          />
                        </FormControl>
                        <SeoSuggestButton
                          form={form}
                          title={form.getValues("title")}
                          content={form.getValues("content")}
                          language={form.getValues("language")}
                        />
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Categories</h3>
                <FormField
                  control={form.control}
                  name="categories"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <div className="space-y-2">
                          {categories?.map((category) => {
                            const label =
                              category.labels.find(
                                (l) => l.language === "en",
                              ) || category.labels[0];
                            return (
                              <div
                                key={category.id}
                                className="flex items-center space-x-2"
                              >
                                <Checkbox
                                  id={category.id}
                                  checked={field.value.includes(category.id)}
                                  onCheckedChange={(checked) => {
                                    if (checked) {
                                      field.onChange([
                                        ...field.value,
                                        category.id,
                                      ]);
                                    } else {
                                      field.onChange(
                                        field.value.filter(
                                          (id) => id !== category.id,
                                        ),
                                      );
                                    }
                                  }}
                                />
                                <Label htmlFor={category.id}>
                                  {label?.name}
                                </Label>
                              </div>
                            );
                          })}
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </div>

          <div className="flex justify-end">
            <Button
              type="submit"
              disabled={mutation.isPending}
              className="min-w-[120px]"
            >
              {mutation.isPending ? "Saving..." : "Save Article"}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
