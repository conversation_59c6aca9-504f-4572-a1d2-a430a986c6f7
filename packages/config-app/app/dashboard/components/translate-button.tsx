import type { BlogListResponse } from "@/app/api/manage/blog/list/route";
import { Languages, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useState } from "react";

const AVAILABLE_LANGUAGES = [
  {
    lang: "en",
    label: "English",
  },
  {
    lang: "ja",
    label: "日本語",
  },
  {
    lang: "zh",
    label: "中文",
  },
];

const addLanguageToArticle = async (
  record: BlogListResponse[0],
  lang: string,
  callback?: () => void,
) => {
  const { contents } = record;
  if (contents.some((content) => content.language === lang)) {
    return;
  }

  const source =
    contents.find(({ language }) => language === "en") || contents[0];

  await fetch("/api/assistance/translate", {
    method: "POST",
    body: JSON.stringify({
      contentId: source.id,
      language: lang,
    }),
  });

  callback?.();
};

export const TranslateButton = ({
  record,
  onSuccess,
}: {
  record: BlogListResponse[0];
  onSuccess?: () => void;
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const { contents } = record;

  const handleTranslate = async (lang: string) => {
    try {
      setIsLoading(true);
      await addLanguageToArticle(record, lang, onSuccess);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" disabled={isLoading}>
          {isLoading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Languages className="h-4 w-4" />
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {AVAILABLE_LANGUAGES.map(({ lang, label }) => {
          const isDisabled = contents.some(
            (content) => content.language === lang,
          );
          return (
            <DropdownMenuItem
              key={lang}
              disabled={isDisabled}
              onClick={() => !isDisabled && handleTranslate(lang)}
            >
              {label}
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
