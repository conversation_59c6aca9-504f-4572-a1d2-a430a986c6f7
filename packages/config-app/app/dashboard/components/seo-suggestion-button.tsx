import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { FormValues } from "../[[...path]]/containers/edit-content-page";
import { UseFormReturn } from "react-hook-form";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Loader2 } from "lucide-react";

interface SeoSuggestions {
  slug: string;
  title: string;
  description: string;
}

export const SeoSuggestButton = ({
  form,
  title,
  content,
  language,
}: {
  form: UseFormReturn<FormValues>;
  title: string;
  content: string;
  language: string;
}) => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const [suggestions, setSuggestions] = useState<SeoSuggestions | null>(null);
  const [selectedFields, setSelectedFields] = useState<Record<string, boolean>>(
    {
      slug: true,
      title: true,
      seoDescription: true,
    },
  );

  const handleGetSuggestions = async () => {
    setIsLoading(true);
    try {
      const res = await fetch("/api/assistance/seo", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          title,
          article: content,
          lang: language,
        }),
      });

      if (!res.ok) {
        throw new Error("Failed to generate SEO suggestions");
      }

      const data: SeoSuggestions = await res.json();
      setSuggestions(data);
      setOpen(true);
    } catch (error) {
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "Failed to generate SEO suggestions.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleApplySelected = () => {
    if (!suggestions) return;

    if (selectedFields.slug) {
      form.setValue("slug", suggestions.slug);
    }
    if (selectedFields.title) {
      form.setValue("title", suggestions.title);
    }
    if (selectedFields.seoDescription) {
      form.setValue("seoDescription", suggestions.description);
    }

    setOpen(false);
    toast({
      title: "SEO Suggestions Applied",
      description: "Selected SEO suggestions have been applied to the form.",
    });
  };

  const toggleField = (field: string) => {
    setSelectedFields((prev) => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  return (
    <>
      <Button disabled={isLoading} onClick={handleGetSuggestions}>
        {isLoading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Generating SEO Suggestions...
          </>
        ) : (
          "Get SEO Suggestions"
        )}
      </Button>

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>SEO Suggestions</DialogTitle>
            <DialogDescription>
              Review and select which suggestions to apply to your content.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4 space-y-6">
            {suggestions && (
              <>
                <div className="space-y-4">
                  <div className="space-y-3">
                    <div
                      className="flex items-start space-x-3 cursor-pointer hover:bg-muted/50 p-2 rounded-md transition-colors"
                      onClick={() => toggleField("slug")}
                    >
                      <Checkbox
                        id="slug"
                        checked={selectedFields.slug}
                        onCheckedChange={() => toggleField("slug")}
                        className="mt-1"
                      />
                      <div className="flex-1">
                        <Label htmlFor="slug" className="text-sm font-medium">
                          URL Slug
                        </Label>
                        <div className="mt-1 p-3 rounded-md bg-muted text-sm font-mono">
                          {suggestions.slug}
                        </div>
                      </div>
                    </div>

                    <div
                      className="flex items-start space-x-3 cursor-pointer hover:bg-muted/50 p-2 rounded-md transition-colors"
                      onClick={() => toggleField("title")}
                    >
                      <Checkbox
                        id="title"
                        checked={selectedFields.title}
                        onCheckedChange={() => toggleField("title")}
                        className="mt-1"
                      />
                      <div className="flex-1">
                        <Label htmlFor="title" className="text-sm font-medium">
                          Title
                        </Label>
                        <div className="mt-1 p-3 rounded-md bg-muted text-sm select-text">
                          {suggestions.title}
                        </div>
                      </div>
                    </div>

                    <div
                      className="flex items-start space-x-3 cursor-pointer hover:bg-muted/50 p-2 rounded-md transition-colors"
                      onClick={() => toggleField("seoDescription")}
                    >
                      <Checkbox
                        id="seoDescription"
                        checked={selectedFields.seoDescription}
                        onCheckedChange={() => toggleField("seoDescription")}
                        className="mt-1"
                      />
                      <div className="flex-1">
                        <Label
                          htmlFor="seoDescription"
                          className="text-sm font-medium"
                        >
                          Meta Description
                        </Label>
                        <div className="mt-1 p-3 rounded-md bg-muted text-sm select-text">
                          {suggestions.description}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="text-sm text-muted-foreground">
                  <p>
                    These suggestions were generated by AI to help optimize your
                    content for search engines.
                  </p>
                </div>
              </>
            )}
          </div>

          <DialogFooter className="gap-2 sm:gap-0">
            <Button variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleApplySelected}>Apply Selected</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};
