import { auth } from "@/auth";
import { prisma } from "prisma-db";
import { z } from "zod";

const createBlogSchema = z.object({
  blog: z.object({
    articleDate: z.string().transform((str) => new Date(str)),
    isPremium: z.boolean(),
    isPublished: z.boolean(),
    slug: z.string(),
    tags: z.array(z.string()).optional().default([]),
    categories: z.array(z.string()).optional().default([]),
  }),
  content: z.object({
    title: z.string().min(1, "Title is required"),
    imageUrl: z.string().optional().default(""),
    seoDescription: z.string().optional().default(""),
    content: z.string().optional().default(""),
    language: z.enum(["en", "jp", "zh"]),
  }),
});

export type CreateBlogInput = z.infer<typeof createBlogSchema>;

// GET - List articles for a specific space (admin only)
export async function GET(
  req: Request,
  { params }: { params: { spaceId: string } },
) {
  const res = await auth();
  if (!res?.user || res.user.role !== "ADMIN") {
    return Response.json({ message: "Unauthorized" }, { status: 401 });
  }

  try {
    const blogs = await prisma.blog.findMany({
      where: { spaceId: params.spaceId },
      include: {
        contents: { orderBy: { language: "asc" } },
        categories: {
          include: {
            labels: true,
          },
        },
      },
      orderBy: { articleDate: "desc" },
    });

    return Response.json(blogs);
  } catch (error) {
    return Response.json(
      {
        message: "Failed to fetch articles",
        error,
      },
      { status: 500 },
    );
  }
}

// POST - Create new article in a specific space (admin only)
export async function POST(
  req: Request,
  { params }: { params: { spaceId: string } },
) {
  const res = await auth();
  if (!res?.user || res.user.role !== "ADMIN") {
    return Response.json({ message: "Unauthorized" }, { status: 401 });
  }

  try {
    const data = await req.json();
    const validatedData = createBlogSchema.parse(data);

    // Create the blog
    const blog = await prisma.blog.create({
      data: {
        ...validatedData.blog,
        spaceId: params.spaceId,
        categories: validatedData.blog.categories?.length
          ? {
              connect: validatedData.blog.categories.map((id) => ({ id })),
            }
          : undefined,
      },
    });

    // Create the content
    const content = await prisma.blogContent.create({
      data: {
        ...validatedData.content,
        blogId: blog.id,
      },
    });

    return Response.json({ blog, content });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return Response.json(
        {
          message: "Invalid article data",
          errors: error.errors,
        },
        { status: 400 },
      );
    }

    return Response.json(
      {
        message: "Failed to create article",
        error,
      },
      { status: 500 },
    );
  }
}
