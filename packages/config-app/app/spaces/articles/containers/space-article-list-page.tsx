"use client";
import React, { useState } from "react";
import { useQuery, useQueryClient, useMutation } from "@tanstack/react-query";
import { useParams } from "next/navigation";
import Link from "next/link";
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { Trash2, ArrowLeft, Edit, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useToast } from "@/hooks/use-toast";

interface BlogContent {
  id: string;
  title: string;
  language: string;
  imageUrl: string;
  seoDescription: string;
  content: string;
  isReady: boolean;
}

interface Blog {
  id: string;
  slug: string;
  articleDate: string;
  isPremium: boolean;
  isPublished: boolean;
  tags: string[];
  contents: BlogContent[];
  categories: Array<{
    id: string;
    labels: Array<{
      id: string;
      name: string;
      language: string;
    }>;
  }>;
}

const fetchSpaceArticles = async (spaceId: string): Promise<Blog[]> => {
  const response = await fetch(`/api/manage/spaces/${spaceId}/articles`);
  if (!response.ok) {
    throw new Error("Failed to fetch articles");
  }
  return response.json();
};

const columnHelper = createColumnHelper<Blog>();

export function SpaceArticleListPage() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const params = useParams();
  const path = params.path as string[] | undefined;
  const spaceId = path?.[0] || "";
  const [operationId, setOperationId] = useState<string | null>(null);

  const { data: articles, isPending } = useQuery({
    queryKey: ["space-articles", spaceId],
    queryFn: () => fetchSpaceArticles(spaceId),
    enabled: !!spaceId,
  });

  const { data: space } = useQuery({
    queryKey: ["space", spaceId],
    queryFn: async () => {
      const response = await fetch(`/api/manage/spaces/${spaceId}`);
      if (!response.ok) {
        throw new Error("Failed to fetch space");
      }
      return response.json();
    },
    enabled: !!spaceId,
  });

  const deleteArticleMutation = useMutation({
    mutationFn: async (articleId: string) => {
      const response = await fetch(
        `/api/manage/spaces/${spaceId}/articles/${articleId}`,
        {
          method: "DELETE",
        },
      );
      if (!response.ok) {
        throw new Error("Failed to delete article");
      }
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Article deleted successfully",
      });
      queryClient.invalidateQueries({ queryKey: ["space-articles", spaceId] });
      setOperationId(null);
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to delete article",
        variant: "destructive",
      });
      console.error("Delete error:", error);
    },
  });

  const revalidateList = () => {
    queryClient.invalidateQueries({ queryKey: ["space-articles", spaceId] });
  };

  const columns = [
    columnHelper.accessor("contents", {
      header: "Title",
      cell: ({ getValue, row }) => {
        const contents = getValue();
        const primaryContent =
          contents.find((c) => c.language === "en") || contents[0];
        return (
          <div className="flex flex-col gap-1">
            <Link
              href={`/spaces/${spaceId}/articles/${row.original.id}/edit`}
              className="font-medium hover:underline"
            >
              {primaryContent?.title || "Untitled"}
            </Link>
            <div className="text-xs text-muted-foreground">
              Languages: {contents.map((c) => c.language).join(", ")}
            </div>
          </div>
        );
      },
    }),
    columnHelper.accessor("articleDate", {
      header: "Date",
      cell: ({ getValue }) => {
        const date = new Date(getValue());
        return date.toLocaleDateString();
      },
    }),
    columnHelper.accessor("isPremium", {
      header: "Premium",
      cell: ({ getValue }) => (getValue() ? "Premium" : ""),
      size: 100,
    }),
    columnHelper.accessor("isPublished", {
      header: "Published",
      cell: ({ getValue }) => (
        <span
          className={`inline-flex items-center ${
            getValue() ? "text-green-600" : "text-gray-500"
          }`}
        >
          {getValue() ? "● Published" : "○ Draft"}
        </span>
      ),
      size: 120,
    }),
    columnHelper.display({
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <div className="flex gap-2">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/spaces/${spaceId}/articles/${row.original.id}/edit`}>
              <Edit className="h-4 w-4" />
            </Link>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setOperationId(row.original.id)}
            className="text-destructive hover:text-destructive"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    }),
  ];

  const table = useReactTable({
    data: articles || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  const handleDelete = () => {
    if (operationId) {
      deleteArticleMutation.mutate(operationId);
    }
  };

  if (isPending) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-lg">Loading articles...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href={`/spaces/${spaceId}`}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Space
          </Link>
        </Button>
      </div>

      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-foreground">
            Articles
          </h1>
          <p className="text-muted-foreground">
            {`Manage articles for "${space?.title}"`}
          </p>
        </div>
        <Button asChild>
          <Link href={`/spaces/${spaceId}/articles/new`}>
            <Plus className="h-4 w-4 mr-2" />
            Create Article
          </Link>
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext(),
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow key={row.id}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No articles found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <Dialog open={!!operationId} onOpenChange={() => setOperationId(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Article</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this article? This action cannot
              be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setOperationId(null)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={deleteArticleMutation.isPending}
            >
              {deleteArticleMutation.isPending ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
