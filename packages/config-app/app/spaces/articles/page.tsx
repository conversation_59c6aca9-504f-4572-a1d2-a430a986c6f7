"use client";
import dynamic from "next/dynamic";
import { useParams } from "next/navigation";
import { EditContentPage } from "./containers/edit-content-page";
import { SpaceArticleListPage } from "./containers/space-article-list-page";
import { Toaster } from "@/components/ui/toaster";
import { SpaceListPage } from "../spaces/containers/space-list-page";
import { SpaceDetailsPage } from "../spaces/[spaceId]/containers/space-details-page";
import { SpaceConfigurationPage } from "../spaces/[spaceId]/config/containers/space-configuration-page";

function SpacesPage() {
  const params = useParams();
  const path = params.path as string[] | undefined;

  // Handle different route patterns
  if (!path || path.length === 0) {
    // /spaces - list all spaces
    return (
      <>
        <SpaceListPage />
        <Toaster />
      </>
    );
  }

  if (path.length === 1) {
    // /spaces/[spaceId] - space details
    return (
      <>
        <SpaceDetailsPage />
        <Toaster />
      </>
    );
  }

  if (path.length === 2 && path[1] === "config") {
    // /spaces/[spaceId]/config - space configuration
    return (
      <>
        <SpaceConfigurationPage />
        <Toaster />
      </>
    );
  }

  if (path.length === 2 && path[1] === "articles") {
    // /spaces/[spaceId]/articles - article list
    return (
      <>
        <SpaceArticleListPage />
        <Toaster />
      </>
    );
  }

  if (path.length === 3 && path[1] === "articles" && path[2] === "new") {
    // /spaces/[spaceId]/articles/new - create new article
    return (
      <>
        <EditContentPage />
        <Toaster />
      </>
    );
  }

  if (path.length === 4 && path[1] === "articles" && path[3] === "edit") {
    // /spaces/[spaceId]/articles/[articleId]/edit - edit article
    const articleId = path[2];
    return (
      <>
        <EditContentPage contentId={articleId} />
        <Toaster />
      </>
    );
  }

  // Default fallback to space list
  return (
    <>
      <SpaceListPage />
      <Toaster />
    </>
  );
}

export default dynamic(() => Promise.resolve(SpacesPage), {
  ssr: false,
});
