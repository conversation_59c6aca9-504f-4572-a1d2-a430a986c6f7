"use client";
import React, { useState } from "react";
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { ArrowLeft, Settings, FileText, Users, Edit } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import Link from "next/link";
import { useParams } from "next/navigation";

const updateSpaceSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().min(1, "Description is required"),
});

type UpdateSpaceFormValues = z.infer<typeof updateSpaceSchema>;

interface Space {
  id: string;
  title: string;
  description: string;
  owner: {
    id: string;
    name: string | null;
    email: string;
  };
  _count: {
    blogs: number;
    Category: number;
  };
}

export function SpaceDetailsPage() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const params = useParams();
  const spaceId = params.spaceId as string;
  const [editDialogOpen, setEditDialogOpen] = useState(false);

  const form = useForm<UpdateSpaceFormValues>({
    resolver: zodResolver(updateSpaceSchema),
    defaultValues: {
      title: "",
      description: "",
    },
  });

  const { data: space, isLoading } = useQuery({
    queryKey: ["space", spaceId],
    queryFn: async () => {
      const response = await fetch(`/api/manage/spaces/${spaceId}`);
      if (!response.ok) {
        throw new Error("Failed to fetch space");
      }
      return response.json() as Promise<Space>;
    },
    enabled: !!spaceId,
  });

  const updateSpaceMutation = useMutation({
    mutationFn: async (data: UpdateSpaceFormValues) => {
      const response = await fetch(`/api/manage/spaces/${spaceId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to update space");
      }

      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Space updated",
        description: "Your space has been updated successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ["space", spaceId] });
      queryClient.invalidateQueries({ queryKey: ["spaces"] });
      setEditDialogOpen(false);
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  React.useEffect(() => {
    if (space) {
      form.reset({
        title: space.title,
        description: space.description,
      });
    }
  }, [space, form]);

  const onSubmit = (data: UpdateSpaceFormValues) => {
    updateSpaceMutation.mutate(data);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-lg">Loading space...</div>
      </div>
    );
  }

  if (!space) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-lg text-muted-foreground">Space not found</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/spaces">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Spaces
          </Link>
        </Button>
      </div>

      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-foreground">
            {space.title}
          </h1>
          <p className="text-muted-foreground">{space.description}</p>
        </div>

        <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="outline">
              <Edit className="h-4 w-4 mr-2" />
              Edit Space
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Edit Space</DialogTitle>
              <DialogDescription>
                Update the space title and description.
              </DialogDescription>
            </DialogHeader>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4"
              >
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Title</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter space title" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter space description"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <DialogFooter>
                  <Button
                    type="submit"
                    disabled={updateSpaceMutation.isPending}
                  >
                    {updateSpaceMutation.isPending
                      ? "Updating..."
                      : "Update Space"}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Space Information</CardTitle>
            <CardDescription>
              Basic information about this space
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Owner
              </label>
              <p className="text-sm">{space.owner.name || space.owner.email}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Space ID
              </label>
              <p className="text-sm font-mono">{space.id}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Content Statistics</CardTitle>
            <CardDescription>Overview of content in this space</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="flex items-center text-sm">
                <FileText className="h-4 w-4 mr-2" />
                Blogs
              </span>
              <span className="text-sm font-medium">{space._count.blogs}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="flex items-center text-sm">
                <Users className="h-4 w-4 mr-2" />
                Categories
              </span>
              <span className="text-sm font-medium">
                {space._count.Category}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle>Articles</CardTitle>
            <CardDescription>
              Manage articles and blog posts for this space
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild className="w-full">
              <Link href={`/spaces/${space.id}/articles`}>
                <FileText className="h-4 w-4 mr-2" />
                Manage Articles
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Configuration</CardTitle>
            <CardDescription>
              Manage environment variables and settings
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild variant="outline" className="w-full">
              <Link href={`/spaces/${space.id}/config`}>
                <Settings className="h-4 w-4 mr-2" />
                Manage Configuration
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Categories</CardTitle>
            <CardDescription>
              Manage content categories for this space
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild variant="outline" className="w-full">
              <Link href="/spaces">
                <Users className="h-4 w-4 mr-2" />
                Manage Categories
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
